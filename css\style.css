/* Reset และ Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-black: #1a1a1a;
    --secondary-black: #2d2d2d;
    --accent-black: #0a0a0a;
    --primary-pink: #ff1493;
    --secondary-pink: #ff69b4;
    --accent-pink: #c71585;
    --primary-gold: #ffd700;
    --secondary-gold: #ffb347;
    --accent-gold: #daa520;
    --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2d2d2d 100%);
    --gradient-pink: linear-gradient(45deg, #ff1493, #ff69b4);
    --gradient-gold: linear-gradient(45deg, #ffd700, #ffb347);
}

body {
    font-family: 'Kanit', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: var(--gradient-dark);
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 20, 147, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 105, 180, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(255, 215, 0, 0.3);
    border-bottom: 2px solid var(--primary-gold);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: var(--primary-gold);
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
    position: relative;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-pink);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 20, 147, 0.1);
    color: var(--primary-pink);
    transform: translateY(-2px);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-auth {
    display: flex;
    gap: 10px;
}

.btn-login,
.btn-register {
    padding: 8px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-login {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-login:hover {
    background: var(--primary-gold);
    color: var(--primary-black);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.btn-register {
    background: var(--gradient-pink);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 20, 147, 0.4);
    border: 1px solid var(--accent-gold);
}

.btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 20, 147, 0.6);
    border-color: var(--primary-gold);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-gold);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    padding: 120px 0 60px;
    background: var(--gradient-dark);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(255, 20, 147, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><text x="12.5" y="18" text-anchor="middle" fill="%23ffd700" opacity="0.1" font-size="14">★</text></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
    pointer-events: none;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-gold), var(--secondary-gold), var(--primary-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.8);
    position: relative;
}

.hero-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-gold);
    border-radius: 2px;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
    line-height: 1.8;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Kanit', sans-serif;
}

.btn-primary {
    background: var(--gradient-pink);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 20, 147, 0.4);
    border: 2px solid var(--primary-gold);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-gold);
    transition: left 0.3s ease;
    z-index: -1;
}

.btn-primary:hover::before {
    left: 0;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 215, 0, 0.6);
    color: var(--primary-black);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-secondary:hover {
    background: var(--primary-gold);
    color: var(--primary-black);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.hero-image {
    text-align: center;
    position: relative;
}

.character-img {
    max-width: 100%;
    height: auto;
    filter:
        drop-shadow(0 10px 30px rgba(255, 20, 147, 0.4))
        drop-shadow(0 0 20px rgba(255, 215, 0, 0.2));
    animation: float 3s ease-in-out infinite;
    border-radius: 20px;
    border: 3px solid transparent;
    background: linear-gradient(var(--primary-black), var(--primary-black)) padding-box,
                var(--gradient-gold) border-box;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Server Status */
.server-status {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin: 60px auto 0;
    max-width: 1200px;
    box-shadow: 0 10px 40px rgba(255, 215, 0, 0.2);
    border: 2px solid var(--primary-gold);
    position: relative;
}

.server-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-dark);
    border-radius: 18px;
    z-index: -1;
}

.status-container h3 {
    color: var(--primary-gold);
    margin-bottom: 20px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.server-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.server-item {
    background: rgba(45, 45, 45, 0.8);
    padding: 20px;
    border-radius: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid var(--accent-gold);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.server-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.server-item:hover::before {
    left: 100%;
}

.server-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 20, 147, 0.3);
    border-color: var(--primary-pink);
}

.server-name {
    font-weight: 600;
    color: var(--primary-gold);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.server-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.server-status.online {
    color: #28a745;
}

.server-status.maintenance {
    color: #ffc107;
}

.server-players {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Download Section */
.download-section {
    padding: 80px 0;
    background: rgba(26, 26, 26, 0.8);
    position: relative;
}

.download-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 20, 147, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    position: relative;
}

.section-subtitle {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 50px;
    font-size: 1.1rem;
}

.download-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.download-card {
    background: rgba(45, 45, 45, 0.9);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(255, 215, 0, 0.15);
    border: 2px solid var(--accent-gold);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 20, 147, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
    pointer-events: none;
}

.download-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 20, 147, 0.25);
    border-color: var(--primary-pink);
}

.download-icon {
    font-size: 3rem;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.5));
}

.download-card h3 {
    color: var(--primary-gold);
    margin-bottom: 10px;
    font-size: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.download-card p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
}

.download-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 25px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

.btn-download {
    background: var(--gradient-pink);
    color: white;
    border: 2px solid var(--primary-gold);
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    font-family: 'Kanit', sans-serif;
    position: relative;
    overflow: hidden;
}

.btn-download::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-gold);
    transition: left 0.3s ease;
    z-index: -1;
}

.btn-download:hover::before {
    left: 0;
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    color: var(--primary-black);
}

/* System Requirements */
.system-requirements {
    background: rgba(45, 45, 45, 0.9);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(255, 215, 0, 0.15);
    border: 2px solid var(--accent-gold);
    position: relative;
}

.system-requirements::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 20, 147, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
    border-radius: 18px;
    pointer-events: none;
}

.system-requirements h3 {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.8rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    position: relative;
    z-index: 1;
}

.requirements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    position: relative;
    z-index: 1;
}

.req-column h4 {
    color: var(--primary-pink);
    margin-bottom: 15px;
    font-size: 1.3rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.req-column ul {
    list-style: none;
}

.req-column li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    padding-left: 20px;
}

.req-column li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--primary-gold);
    font-size: 0.8rem;
}

.req-column li:last-child {
    border-bottom: none;
}

/* Ranking Section */
.ranking-section {
    padding: 80px 0;
    background: var(--gradient-dark);
    position: relative;
}

.ranking-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 20, 147, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.ranking-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;
}

.tab-btn {
    padding: 12px 30px;
    border: 2px solid var(--primary-gold);
    background: transparent;
    color: var(--primary-gold);
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-pink);
    transition: left 0.3s ease;
    z-index: -1;
}

.tab-btn.active::before,
.tab-btn:hover::before {
    left: 0;
}

.tab-btn.active,
.tab-btn:hover {
    color: white;
    transform: translateY(-2px);
    border-color: var(--primary-pink);
}

.ranking-content {
    background: rgba(45, 45, 45, 0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(255, 215, 0, 0.2);
    border: 2px solid var(--primary-gold);
    position: relative;
    z-index: 1;
}

.ranking-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 20, 147, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
    pointer-events: none;
    border-radius: 18px;
}

.ranking-table {
    display: none;
    position: relative;
    z-index: 1;
}

.ranking-table.active {
    display: block;
}

.ranking-header {
    background: linear-gradient(45deg, var(--primary-black), var(--secondary-black));
    color: var(--primary-gold);
    padding: 20px;
    display: grid;
    grid-template-columns: 80px 1fr 100px 120px 120px;
    gap: 20px;
    font-weight: 600;
    text-align: center;
    border-bottom: 3px solid var(--primary-gold);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.ranking-list {
    max-height: 500px;
    overflow-y: auto;
}

.ranking-item {
    padding: 20px;
    display: grid;
    grid-template-columns: 80px 1fr 100px 120px 120px;
    gap: 20px;
    align-items: center;
    text-align: center;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
}

.ranking-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-pink);
    transition: width 0.3s ease;
    opacity: 0.1;
}

.ranking-item:hover::before {
    width: 100%;
}

.ranking-item:hover {
    background: rgba(255, 20, 147, 0.05);
    border-color: var(--primary-pink);
}

.ranking-item.rank-1 {
    background: linear-gradient(90deg, rgba(255, 215, 0, 0.15), rgba(45, 45, 45, 0.9));
    border-color: var(--primary-gold);
}

.ranking-item.rank-2 {
    background: linear-gradient(90deg, rgba(192, 192, 192, 0.15), rgba(45, 45, 45, 0.9));
    border-color: #c0c0c0;
}

.ranking-item.rank-3 {
    background: linear-gradient(90deg, rgba(205, 127, 50, 0.15), rgba(45, 45, 45, 0.9));
    border-color: #cd7f32;
}

.rank {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--primary-gold);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.ranking-item.rank-1 .rank {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.player-name {
    font-weight: 600;
    color: var(--primary-pink);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.level {
    font-weight: 600;
    color: var(--secondary-gold);
}

.class {
    color: rgba(255, 255, 255, 0.7);
}

.server {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, var(--secondary-black) 0%, var(--primary-black) 100%);
    margin: 5% auto;
    padding: 40px;
    border-radius: 20px;
    width: 90%;
    max-width: 450px;
    position: relative;
    box-shadow: 0 20px 60px rgba(255, 215, 0, 0.3);
    border: 2px solid var(--primary-gold);
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 20, 147, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
    border-radius: 18px;
    pointer-events: none;
}

.close {
    color: var(--primary-gold);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 15px;
    right: 20px;
    transition: color 0.3s ease;
    z-index: 1;
}

.close:hover {
    color: var(--primary-pink);
}

.modal-content h2 {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    position: relative;
    z-index: 1;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--primary-gold);
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--accent-gold);
    border-radius: 10px;
    font-size: 1rem;
    background: rgba(45, 45, 45, 0.8);
    color: white;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
    position: relative;
    z-index: 1;
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-pink);
    box-shadow: 0 0 10px rgba(255, 20, 147, 0.3);
    background: rgba(45, 45, 45, 0.9);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 1;
}

.checkbox-container input {
    margin-right: 8px;
}

.forgot-password,
.terms-link {
    color: var(--primary-pink);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    position: relative;
    z-index: 1;
}

.forgot-password:hover,
.terms-link:hover {
    color: var(--secondary-pink);
}

.btn-submit {
    width: 100%;
    padding: 15px;
    background: var(--gradient-pink);
    color: white;
    border: 2px solid var(--primary-gold);
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-gold);
    transition: left 0.3s ease;
    z-index: -1;
}

.btn-submit:hover::before {
    left: 0;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    color: var(--primary-black);
}

.modal-footer {
    text-align: center;
    margin-top: 20px;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 1;
}

.modal-footer a {
    color: var(--primary-pink);
    text-decoration: none;
    font-weight: 600;
}

.modal-footer a:hover {
    color: var(--secondary-pink);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(255, 182, 193, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-auth {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 182, 193, 0.95);
        padding: 15px;
        border-radius: 25px;
        box-shadow: 0 5px 20px rgba(255, 105, 180, 0.3);
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .server-list {
        grid-template-columns: 1fr;
    }

    .server-item {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .download-options {
        grid-template-columns: 1fr;
    }

    .requirements-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .ranking-header,
    .ranking-item {
        grid-template-columns: 60px 1fr 80px;
        gap: 10px;
    }

    .ranking-header span:nth-child(4),
    .ranking-header span:nth-child(5),
    .ranking-item span:nth-child(4),
    .ranking-item span:nth-child(5) {
        display: none;
    }

    .modal-content {
        margin: 10% auto;
        padding: 30px 20px;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .news-card.featured {
        grid-row: span 1;
    }

    .guide-categories {
        grid-template-columns: 1fr;
    }

    .video-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }

    .download-badges {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 12px 20px;
        font-size: 1rem;
    }

    .ranking-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 200px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 105, 180, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 105, 180, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 105, 180, 0);
    }
}

.hero-text {
    animation: fadeInLeft 1s ease-out;
}

.hero-image {
    animation: fadeInRight 1s ease-out;
}

.download-card {
    animation: fadeInUp 0.6s ease-out;
}

.download-card:nth-child(2) {
    animation-delay: 0.2s;
}

.download-card:nth-child(3) {
    animation-delay: 0.4s;
}

.server-status {
    animation: fadeInUp 0.8s ease-out;
}

.btn-primary {
    animation: pulse 2s infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 182, 193, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ff1493, #d63384);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* News Section */
.news-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.5);
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.news-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 105, 180, 0.25);
}

.news-card.featured {
    grid-row: span 2;
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-card.featured .news-image {
    height: 300px;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.news-badge.event {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.news-badge.tournament {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.news-content {
    padding: 25px;
}

.news-content h3 {
    color: #d63384;
    margin-bottom: 15px;
    font-size: 1.3rem;
    line-height: 1.4;
}

.news-card.featured .news-content h3 {
    font-size: 1.5rem;
}

.news-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.news-date {
    color: #888;
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-category {
    background: rgba(255, 105, 180, 0.1);
    color: #ff69b4;
    padding: 3px 10px;
    border-radius: 10px;
    font-weight: 600;
}

.news-more {
    text-align: center;
}

/* Guide Section */
.guide-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe0f0 100%);
}

.guide-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.guide-category {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.guide-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 105, 180, 0.25);
}

.guide-icon {
    font-size: 3rem;
    color: #ff69b4;
    margin-bottom: 20px;
}

.guide-category h3 {
    color: #d63384;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.guide-category p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
}

.guide-category ul {
    list-style: none;
    text-align: left;
    margin-bottom: 30px;
}

.guide-category li {
    padding: 8px 0;
    color: #555;
    position: relative;
    padding-left: 20px;
}

.guide-category li::before {
    content: '♥';
    position: absolute;
    left: 0;
    color: #ff69b4;
    font-weight: bold;
}

.btn-guide {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-guide:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.video-guide {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.video-guide h3 {
    color: #d63384;
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.8rem;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.video-item {
    text-align: center;
}

.video-thumbnail {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 15px;
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 105, 180, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-thumbnail:hover .play-button {
    background: rgba(255, 105, 180, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-item h4 {
    color: #d63384;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.video-item p {
    color: #888;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 50%, var(--accent-black) 100%);
    color: white;
    padding: 60px 0 20px;
    position: relative;
    border-top: 3px solid var(--primary-gold);
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 20, 147, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-stars" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><text x="20" y="25" text-anchor="middle" fill="%23ffd700" opacity="0.05" font-size="18">★</text></pattern></defs><rect width="100" height="100" fill="url(%23footer-stars)"/></svg>');
    pointer-events: none;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 40px;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #fff;
    font-weight: 600;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.footer-logo .logo-text {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.footer-description {
    line-height: 1.6;
    margin-bottom: 25px;
    color: rgba(255, 255, 255, 0.8);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 215, 0, 0.1);
    border: 2px solid var(--accent-gold);
    border-radius: 50%;
    color: var(--primary-gold);
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-pink);
    transform: scale(0);
    transition: transform 0.3s ease;
    border-radius: 50%;
}

.social-link:hover::before {
    transform: scale(1);
}

.social-link:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 20, 147, 0.4);
    border-color: var(--primary-pink);
}

.social-link i {
    position: relative;
    z-index: 1;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-links a::before {
    content: '→';
    opacity: 0;
    transition: opacity 0.3s ease;
}

.footer-links a:hover::before {
    opacity: 1;
}

.newsletter {
    display: flex;
    margin-bottom: 25px;
    border-radius: 25px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.newsletter-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: white;
    font-family: 'Kanit', sans-serif;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
    outline: none;
}

.newsletter-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 12px 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.download-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.store-badge {
    height: 40px;
    transition: transform 0.3s ease;
}

.badge-link:hover .store-badge {
    transform: scale(1.05);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 20px;
    position: relative;
    z-index: 1;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom-links {
    display: flex;
    gap: 30px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
    color: white;
}

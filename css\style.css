/* Reset และ Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe0f0 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 182, 193, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(255, 105, 180, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: #d63384;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #d63384;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 20px;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(214, 51, 132, 0.1);
    color: #b02a5b;
    transform: translateY(-2px);
}

.nav-auth {
    display: flex;
    gap: 10px;
}

.btn-login,
.btn-register {
    padding: 8px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-login {
    background: transparent;
    color: #d63384;
    border: 2px solid #d63384;
}

.btn-login:hover {
    background: #d63384;
    color: white;
    transform: translateY(-2px);
}

.btn-register {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
}

.btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 105, 180, 0.6);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #d63384;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe0f0 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="%23ff69b4" opacity="0.1" font-size="12">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
    pointer-events: none;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff69b4, #ff1493, #d63384);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Kanit', sans-serif;
}

.btn-primary {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 105, 180, 0.6);
}

.btn-secondary {
    background: transparent;
    color: #d63384;
    border: 2px solid #d63384;
}

.btn-secondary:hover {
    background: #d63384;
    color: white;
    transform: translateY(-3px);
}

.hero-image {
    text-align: center;
    position: relative;
}

.character-img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(255, 105, 180, 0.3));
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Server Status */
.server-status {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin: 60px auto 0;
    max-width: 1200px;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.2);
    border: 1px solid rgba(255, 182, 193, 0.3);
}

.status-container h3 {
    color: #d63384;
    margin-bottom: 20px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.server-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.server-item {
    background: rgba(255, 240, 245, 0.8);
    padding: 20px;
    border-radius: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.server-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(255, 105, 180, 0.2);
}

.server-name {
    font-weight: 600;
    color: #333;
}

.server-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.server-status.online {
    color: #28a745;
}

.server-status.maintenance {
    color: #ffc107;
}

.server-players {
    color: #666;
    font-size: 0.9rem;
}

/* Download Section */
.download-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.5);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: #d63384;
    margin-bottom: 10px;
    font-weight: 700;
}

.section-subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 50px;
    font-size: 1.1rem;
}

.download-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.download-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.download-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 105, 180, 0.25);
}

.download-icon {
    font-size: 3rem;
    color: #ff69b4;
    margin-bottom: 20px;
}

.download-card h3 {
    color: #d63384;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.download-card p {
    color: #666;
    margin-bottom: 20px;
}

.download-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 25px;
    font-size: 0.9rem;
    color: #888;
}

.btn-download {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    font-family: 'Kanit', sans-serif;
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

/* System Requirements */
.system-requirements {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.system-requirements h3 {
    color: #d63384;
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.8rem;
}

.requirements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.req-column h4 {
    color: #ff69b4;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.req-column ul {
    list-style: none;
}

.req-column li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 182, 193, 0.3);
    color: #555;
}

.req-column li:last-child {
    border-bottom: none;
}

/* Ranking Section */
.ranking-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe0f0 100%);
}

.ranking-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 12px 30px;
    border: 2px solid #ff69b4;
    background: transparent;
    color: #ff69b4;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.tab-btn.active,
.tab-btn:hover {
    background: #ff69b4;
    color: white;
    transform: translateY(-2px);
}

.ranking-content {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.2);
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.ranking-table {
    display: none;
}

.ranking-table.active {
    display: block;
}

.ranking-header {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    padding: 20px;
    display: grid;
    grid-template-columns: 80px 1fr 100px 120px 120px;
    gap: 20px;
    font-weight: 600;
    text-align: center;
}

.ranking-list {
    max-height: 500px;
    overflow-y: auto;
}

.ranking-item {
    padding: 20px;
    display: grid;
    grid-template-columns: 80px 1fr 100px 120px 120px;
    gap: 20px;
    align-items: center;
    text-align: center;
    border-bottom: 1px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: rgba(255, 240, 245, 0.8);
}

.ranking-item.rank-1 {
    background: linear-gradient(90deg, rgba(255, 215, 0, 0.1), rgba(255, 255, 255, 0.9));
}

.ranking-item.rank-2 {
    background: linear-gradient(90deg, rgba(192, 192, 192, 0.1), rgba(255, 255, 255, 0.9));
}

.ranking-item.rank-3 {
    background: linear-gradient(90deg, rgba(205, 127, 50, 0.1), rgba(255, 255, 255, 0.9));
}

.rank {
    font-weight: 700;
    font-size: 1.2rem;
    color: #d63384;
}

.player-name {
    font-weight: 600;
    color: #333;
}

.level {
    font-weight: 600;
    color: #ff69b4;
}

.class {
    color: #666;
}

.server {
    color: #888;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 100%);
    margin: 5% auto;
    padding: 40px;
    border-radius: 20px;
    width: 90%;
    max-width: 450px;
    position: relative;
    box-shadow: 0 20px 60px rgba(255, 105, 180, 0.3);
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.close {
    color: #d63384;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 15px;
    right: 20px;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff1493;
}

.modal-content h2 {
    color: #d63384;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #d63384;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 182, 193, 0.5);
    border-radius: 10px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.form-group input:focus {
    outline: none;
    border-color: #ff69b4;
    box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-container input {
    margin-right: 8px;
}

.forgot-password,
.terms-link {
    color: #ff69b4;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover,
.terms-link:hover {
    color: #ff1493;
}

.btn-submit {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.modal-footer {
    text-align: center;
    margin-top: 20px;
    color: #666;
}

.modal-footer a {
    color: #ff69b4;
    text-decoration: none;
    font-weight: 600;
}

.modal-footer a:hover {
    color: #ff1493;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(255, 182, 193, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-auth {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(255, 182, 193, 0.95);
        padding: 15px;
        border-radius: 25px;
        box-shadow: 0 5px 20px rgba(255, 105, 180, 0.3);
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .server-list {
        grid-template-columns: 1fr;
    }

    .server-item {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .download-options {
        grid-template-columns: 1fr;
    }

    .requirements-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .ranking-header,
    .ranking-item {
        grid-template-columns: 60px 1fr 80px;
        gap: 10px;
    }

    .ranking-header span:nth-child(4),
    .ranking-header span:nth-child(5),
    .ranking-item span:nth-child(4),
    .ranking-item span:nth-child(5) {
        display: none;
    }

    .modal-content {
        margin: 10% auto;
        padding: 30px 20px;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .news-card.featured {
        grid-row: span 1;
    }

    .guide-categories {
        grid-template-columns: 1fr;
    }

    .video-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }

    .download-badges {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 12px 20px;
        font-size: 1rem;
    }

    .ranking-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 200px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 105, 180, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 105, 180, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 105, 180, 0);
    }
}

.hero-text {
    animation: fadeInLeft 1s ease-out;
}

.hero-image {
    animation: fadeInRight 1s ease-out;
}

.download-card {
    animation: fadeInUp 0.6s ease-out;
}

.download-card:nth-child(2) {
    animation-delay: 0.2s;
}

.download-card:nth-child(3) {
    animation-delay: 0.4s;
}

.server-status {
    animation: fadeInUp 0.8s ease-out;
}

.btn-primary {
    animation: pulse 2s infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 182, 193, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ff1493, #d63384);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* News Section */
.news-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.5);
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.news-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 105, 180, 0.25);
}

.news-card.featured {
    grid-row: span 2;
}

.news-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.news-card.featured .news-image {
    height: 300px;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.news-badge.event {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.news-badge.tournament {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.news-content {
    padding: 25px;
}

.news-content h3 {
    color: #d63384;
    margin-bottom: 15px;
    font-size: 1.3rem;
    line-height: 1.4;
}

.news-card.featured .news-content h3 {
    font-size: 1.5rem;
}

.news-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.news-date {
    color: #888;
    display: flex;
    align-items: center;
    gap: 5px;
}

.news-category {
    background: rgba(255, 105, 180, 0.1);
    color: #ff69b4;
    padding: 3px 10px;
    border-radius: 10px;
    font-weight: 600;
}

.news-more {
    text-align: center;
}

/* Guide Section */
.guide-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe0f0 100%);
}

.guide-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.guide-category {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
}

.guide-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(255, 105, 180, 0.25);
}

.guide-icon {
    font-size: 3rem;
    color: #ff69b4;
    margin-bottom: 20px;
}

.guide-category h3 {
    color: #d63384;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.guide-category p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.6;
}

.guide-category ul {
    list-style: none;
    text-align: left;
    margin-bottom: 30px;
}

.guide-category li {
    padding: 8px 0;
    color: #555;
    position: relative;
    padding-left: 20px;
}

.guide-category li::before {
    content: '♥';
    position: absolute;
    left: 0;
    color: #ff69b4;
    font-weight: bold;
}

.btn-guide {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Kanit', sans-serif;
}

.btn-guide:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
}

.video-guide {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(255, 105, 180, 0.15);
    border: 2px solid rgba(255, 182, 193, 0.3);
}

.video-guide h3 {
    color: #d63384;
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.8rem;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.video-item {
    text-align: center;
}

.video-thumbnail {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 15px;
    cursor: pointer;
}

.video-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-thumbnail:hover img {
    transform: scale(1.05);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 105, 180, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.video-thumbnail:hover .play-button {
    background: rgba(255, 105, 180, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-item h4 {
    color: #d63384;
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.video-item p {
    color: #888;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #d63384 0%, #ff69b4 50%, #ff1493 100%);
    color: white;
    padding: 60px 0 20px;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-hearts" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><text x="15" y="20" text-anchor="middle" fill="%23ffffff" opacity="0.05" font-size="16">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23footer-hearts)"/></svg>');
    pointer-events: none;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 40px;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #fff;
    font-weight: 600;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.footer-logo .logo-text {
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.footer-description {
    line-height: 1.6;
    margin-bottom: 25px;
    color: rgba(255, 255, 255, 0.9);
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-links a::before {
    content: '→';
    opacity: 0;
    transition: opacity 0.3s ease;
}

.footer-links a:hover::before {
    opacity: 1;
}

.newsletter {
    display: flex;
    margin-bottom: 25px;
    border-radius: 25px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.newsletter-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: white;
    font-family: 'Kanit', sans-serif;
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
    outline: none;
}

.newsletter-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 12px 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.download-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.store-badge {
    height: 40px;
    transition: transform 0.3s ease;
}

.badge-link:hover .store-badge {
    transform: scale(1.05);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 20px;
    position: relative;
    z-index: 1;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom-links {
    display: flex;
    gap: 30px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
    color: white;
}

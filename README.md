# Sweet Cabal - เว็บไซต์เกมออนไลน์

เว็บไซต์เกม Sweet Cabal ที่ออกแบบด้วยโทนสีชมพูสุดน่ารัก พร้อมระบบครบครันสำหรับเกมออนไลน์

## ✨ ฟีเจอร์หลัก

### 🏠 หน้าหลัก
- Hero Section พร้อมข้อมูลเกม
- สถานะเซิร์ฟเวอร์แบบเรียลไทม์
- ปุ่มดาวน์โหลดและดูตัวอย่าง

### 📥 ระบบดาวน์โหลด
- รองรับหลายแพลตฟอร์ม (Windows, macOS, Android)
- แสดงข้อมูลขนาดไฟล์และเวอร์ชัน
- ความต้องการของระบบ

### 🏆 ระบบจัดอันดับ
- อันดับเลเวลผู้เล่น
- อันดับ PvP
- อันดับกิลด์
- อัพเดทแบบเรียลไทม์

### 📰 ข่าวสารและกิจกรรม
- ข่าวสารล่าสุด
- กิจกรรมพิเศษ
- การแข่งขันต่างๆ

### 📚 คู่มือและวิธีเล่น
- คู่มือสำหรับผู้เล่นใหม่
- เทคนิคการต่อสู้
- ระบบกิลด์
- วิดีโอสอนเล่น

### 🔐 ระบบสมาชิก
- ระบบเข้าสู่ระบบ
- ระบบสมัครสมาชิก
- การจดจำการเข้าสู่ระบบ
- ระบบรีเซ็ตรหัสผ่าน

## 🎨 การออกแบบ

### สีหลัก
- **Primary Pink**: #ff69b4 (Hot Pink)
- **Secondary Pink**: #ff1493 (Deep Pink)
- **Accent Pink**: #d63384 (Pink)
- **Background**: Linear gradients ในโทนสีชมพู

### ฟอนต์
- **Kanit** - ฟอนต์ไทยที่อ่านง่ายและสวยงาม
- รองรับน้ำหนักต่างๆ (300, 400, 500, 600, 700)

### ไอคอน
- **Font Awesome 6** - ไอคอนครบครันและสวยงาม

## 📱 Responsive Design

เว็บไซต์รองรับการแสดงผลบนอุปกรณ์ต่างๆ:
- 🖥️ Desktop (1200px+)
- 💻 Laptop (768px - 1199px)
- 📱 Mobile (< 768px)

## 🚀 เทคโนโลยีที่ใช้

- **HTML5** - โครงสร้างเว็บไซต์
- **CSS3** - การจัดแต่งและ Animation
- **JavaScript (ES6+)** - ฟังก์ชันการทำงาน
- **Font Awesome** - ไอคอน
- **Google Fonts** - ฟอนต์

## 📂 โครงสร้างไฟล์

```
sweetcabal/
├── index.html              # หน้าหลัก
├── css/
│   └── style.css          # ไฟล์ CSS หลัก
├── js/
│   └── script.js          # ไฟล์ JavaScript หลัก
├── images/
│   ├── placeholder.txt    # คำแนะนำสำหรับรูปภาพ
│   ├── logo.png          # โลโก้เกม (ต้องเพิ่ม)
│   ├── hero-character.png # ตัวละครหลัก (ต้องเพิ่ม)
│   └── ...               # รูปภาพอื่นๆ
└── README.md             # ไฟล์นี้
```

## 🛠️ การติดตั้งและใช้งาน

1. **Clone หรือ Download โปรเจกต์**
   ```bash
   git clone [repository-url]
   cd sweetcabal
   ```

2. **เพิ่มรูปภาพ**
   - เพิ่มไฟล์รูปภาพในโฟลเดอร์ `images/`
   - ดูรายการรูปภาพที่ต้องการใน `images/placeholder.txt`

3. **เปิดเว็บไซต์**
   - เปิดไฟล์ `index.html` ในเบราว์เซอร์
   - หรือใช้ Local Server เช่น Live Server ใน VS Code

## 🎯 ฟีเจอร์พิเศษ

### ⚡ Animation และ Effects
- Smooth scrolling
- Hover effects
- Loading animations
- Floating animations
- Gradient backgrounds

### 📊 ระบบแบบเรียลไทม์
- อัพเดทสถานะเซิร์ฟเวอร์
- การแสดงจำนวนผู้เล่นออนไลน์
- ระบบแจ้งเตือน (Notifications)

### 🔧 ฟังก์ชันพิเศษ
- Mobile-friendly navigation
- Modal system
- Form validation
- Local storage support
- SEO optimized

## 🎮 การปรับแต่ง

### เปลี่ยนสี
แก้ไขตัวแปรสีใน `css/style.css`:
```css
/* เปลี่ยนสีหลัก */
:root {
    --primary-pink: #ff69b4;
    --secondary-pink: #ff1493;
    --accent-pink: #d63384;
}
```

### เพิ่มเนื้อหา
- แก้ไขข้อความใน `index.html`
- เพิ่มข่าวสารใหม่ในส่วน News Section
- อัพเดทข้อมูลการจัดอันดับใน JavaScript

### เพิ่มภาษา
- เพิ่มระบบ i18n ใน JavaScript
- สร้างไฟล์ภาษาแยกต่างหาก

## 🐛 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย
1. **รูปภาพไม่แสดง**: ตรวจสอบ path ของรูปภาพใน `images/`
2. **ฟอนต์ไม่โหลด**: ตรวจสอบการเชื่อมต่ออินเทอร์เน็ต
3. **JavaScript ไม่ทำงาน**: ตรวจสอบ Console ในเบราว์เซอร์

## 📞 การสนับสนุน

หากมีปัญหาหรือข้อสงสัย:
1. ตรวจสอบ Console ในเบราว์เซอร์
2. ดู Network tab สำหรับไฟล์ที่โหลดไม่ได้
3. ตรวจสอบ path ของไฟล์ต่างๆ

## 📄 License

โปรเจกต์นี้เป็น Open Source สามารถนำไปใช้และปรับแต่งได้ตามต้องการ

## 🎉 ขอบคุณ

ขอบคุณสำหรับการใช้งาน Sweet Cabal Website Template!
หวังว่าจะเป็นประโยชน์สำหรับโปรเจกต์เกมของคุณ 💖

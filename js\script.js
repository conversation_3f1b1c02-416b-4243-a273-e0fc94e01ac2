// Mobile Navigation
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Smooth scrolling for navigation links
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Update active navigation link on scroll
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Modal Functions
function openLoginModal() {
    document.getElementById('loginModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function openRegisterModal() {
    document.getElementById('registerModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';
}

function switchToRegister() {
    closeModal('loginModal');
    openRegisterModal();
}

function switchToLogin() {
    closeModal('registerModal');
    openLoginModal();
}

// Close modal when clicking outside
window.addEventListener('click', (event) => {
    const loginModal = document.getElementById('loginModal');
    const registerModal = document.getElementById('registerModal');
    
    if (event.target === loginModal) {
        closeModal('loginModal');
    }
    if (event.target === registerModal) {
        closeModal('registerModal');
    }
});

// Form Validation and Submission
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
        return;
    }
    
    // Simulate login process
    showLoading('กำลังเข้าสู่ระบบ...');
    
    setTimeout(() => {
        hideLoading();
        showNotification('เข้าสู่ระบบสำเร็จ!', 'success');
        closeModal('loginModal');
        updateUserInterface(username);
    }, 2000);
});

document.getElementById('registerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.getElementById('regUsername').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('regPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // Validation
    if (!username || !email || !password || !confirmPassword) {
        showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
        return;
    }
    
    if (password !== confirmPassword) {
        showNotification('รหัสผ่านไม่ตรงกัน', 'error');
        return;
    }
    
    if (password.length < 6) {
        showNotification('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร', 'error');
        return;
    }
    
    if (!agreeTerms) {
        showNotification('กรุณายอมรับข้อกำหนดการใช้งาน', 'error');
        return;
    }
    
    // Simulate registration process
    showLoading('กำลังสมัครสมาชิก...');
    
    setTimeout(() => {
        hideLoading();
        showNotification('สมัครสมาชิกสำเร็จ!', 'success');
        closeModal('registerModal');
        updateUserInterface(username);
    }, 2000);
});

// Ranking System
function showRanking(type) {
    // Hide all ranking tables
    document.querySelectorAll('.ranking-table').forEach(table => {
        table.classList.remove('active');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected ranking table
    document.getElementById(`${type}-ranking`).classList.add('active');
    
    // Add active class to clicked tab
    event.target.classList.add('active');
    
    // Load ranking data (simulate API call)
    loadRankingData(type);
}

function loadRankingData(type) {
    const rankingData = {
        level: [
            { rank: 1, name: 'SweetPrincess', level: 999, class: 'Mage', server: 'Sweet' },
            { rank: 2, name: 'CabalKing', level: 987, class: 'Warrior', server: 'Cabal' },
            { rank: 3, name: 'MagicQueen', level: 976, class: 'Archer', server: 'Magic' },
            { rank: 4, name: 'PinkKnight', level: 965, class: 'Knight', server: 'Sweet' },
            { rank: 5, name: 'CuteAssassin', level: 954, class: 'Assassin', server: 'Cabal' }
        ],
        pvp: [
            { rank: 1, name: 'PvPMaster', level: 850, class: 'Warrior', server: 'Cabal' },
            { rank: 2, name: 'SwordDancer', level: 823, class: 'Knight', server: 'Sweet' },
            { rank: 3, name: 'MagicBlade', level: 801, class: 'Mage', server: 'Magic' }
        ],
        guild: [
            { rank: 1, name: 'Sweet Angels', level: 'Lv.50', class: '50 สมาชิก', server: 'Sweet' },
            { rank: 2, name: 'Cabal Warriors', level: 'Lv.48', class: '45 สมาชิก', server: 'Cabal' },
            { rank: 3, name: 'Magic Circle', level: 'Lv.46', class: '42 สมาชิก', server: 'Magic' }
        ]
    };
    
    const data = rankingData[type] || rankingData.level;
    updateRankingDisplay(data, type);
}

function updateRankingDisplay(data, type) {
    const rankingList = document.querySelector(`#${type}-ranking .ranking-list`);
    
    rankingList.innerHTML = data.map(item => `
        <div class="ranking-item ${item.rank <= 3 ? `rank-${item.rank}` : ''}">
            <span class="rank">${item.rank}</span>
            <span class="player-name">${item.name}</span>
            <span class="level">${item.level}</span>
            <span class="class">${item.class}</span>
            <span class="server">${item.server}</span>
        </div>
    `).join('');
}

// Server Status Update
function updateServerStatus() {
    const servers = [
        { name: 'Sweet', status: 'online', players: Math.floor(Math.random() * 500) + 1000 },
        { name: 'Cabal', status: 'online', players: Math.floor(Math.random() * 400) + 800 },
        { name: 'Magic', status: Math.random() > 0.7 ? 'maintenance' : 'online', players: Math.floor(Math.random() * 300) + 600 }
    ];
    
    const serverItems = document.querySelectorAll('.server-item');
    
    servers.forEach((server, index) => {
        if (serverItems[index]) {
            const statusElement = serverItems[index].querySelector('.server-status');
            const playersElement = serverItems[index].querySelector('.server-players');
            
            statusElement.className = `server-status ${server.status}`;
            statusElement.innerHTML = server.status === 'online' 
                ? '<i class="fas fa-circle"></i> ออนไลน์'
                : '<i class="fas fa-tools"></i> ปรับปรุง';
            
            playersElement.textContent = server.status === 'online' 
                ? `ผู้เล่น: ${server.players}/2,000`
                : 'กำลังปรับปรุง';
        }
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add notification styles if not exists
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 3000;
                display: flex;
                align-items: center;
                gap: 10px;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            }
            .notification.success { background: linear-gradient(45deg, #28a745, #20c997); }
            .notification.error { background: linear-gradient(45deg, #dc3545, #fd7e14); }
            .notification.info { background: linear-gradient(45deg, #17a2b8, #6f42c1); }
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideInRight 0.3s ease-out reverse';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function showLoading(message) {
    const loading = document.createElement('div');
    loading.id = 'loading-overlay';
    loading.innerHTML = `
        <div class="loading-content">
            <div class="loading"></div>
            <p>${message}</p>
        </div>
    `;
    
    // Add loading styles if not exists
    if (!document.querySelector('#loading-styles')) {
        const styles = document.createElement('style');
        styles.id = 'loading-styles';
        styles.textContent = `
            #loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 4000;
                backdrop-filter: blur(5px);
            }
            .loading-content {
                background: rgba(255, 255, 255, 0.95);
                padding: 40px;
                border-radius: 20px;
                text-align: center;
                box-shadow: 0 20px 60px rgba(255, 105, 180, 0.3);
            }
            .loading-content p {
                margin-top: 20px;
                color: #d63384;
                font-weight: 600;
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(loading);
}

function hideLoading() {
    const loading = document.getElementById('loading-overlay');
    if (loading) {
        loading.remove();
    }
}

function updateUserInterface(username) {
    const authButtons = document.querySelector('.nav-auth');
    authButtons.innerHTML = `
        <span class="user-welcome">สวัสดี, ${username}</span>
        <button class="btn-logout" onclick="logout()">ออกจากระบบ</button>
    `;
}

function logout() {
    const authButtons = document.querySelector('.nav-auth');
    authButtons.innerHTML = `
        <button class="btn-login" onclick="openLoginModal()">เข้าสู่ระบบ</button>
        <button class="btn-register" onclick="openRegisterModal()">สมัครสมาชิก</button>
    `;
    showNotification('ออกจากระบบสำเร็จ', 'info');
}

// Server Panel Toggle
function toggleServerPanel() {
    const panelContent = document.querySelector('.panel-content');
    const panelToggle = document.querySelector('.panel-toggle');

    panelContent.classList.toggle('collapsed');
    panelToggle.classList.toggle('collapsed');
}

// Trailer Modal
function openTrailer() {
    // Create trailer modal
    const modal = document.createElement('div');
    modal.className = 'trailer-modal';
    modal.innerHTML = `
        <div class="trailer-content">
            <span class="close-trailer" onclick="closeTrailer()">&times;</span>
            <iframe width="800" height="450"
                src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                frameborder="0" allowfullscreen>
            </iframe>
        </div>
    `;

    // Add trailer modal styles
    if (!document.querySelector('#trailer-styles')) {
        const styles = document.createElement('style');
        styles.id = 'trailer-styles';
        styles.textContent = `
            .trailer-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 5000;
                backdrop-filter: blur(10px);
            }
            .trailer-content {
                position: relative;
                background: var(--primary-black);
                padding: 20px;
                border-radius: 15px;
                border: 2px solid var(--primary-gold);
            }
            .close-trailer {
                position: absolute;
                top: -10px;
                right: -10px;
                background: var(--primary-pink);
                color: white;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-size: 18px;
                font-weight: bold;
                z-index: 1;
            }
            @media (max-width: 768px) {
                .trailer-content iframe {
                    width: 90vw;
                    height: 50vw;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

function closeTrailer() {
    const modal = document.querySelector('.trailer-modal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}

// Enhanced Server Status Update
function updateServerStatus() {
    const servers = [
        {
            name: 'Sweet Server',
            status: 'online',
            players: Math.floor(Math.random() * 500) + 1000,
            maxPlayers: 2000
        },
        {
            name: 'Cabal Server',
            status: 'online',
            players: Math.floor(Math.random() * 400) + 800,
            maxPlayers: 2000
        },
        {
            name: 'Magic Server',
            status: Math.random() > 0.7 ? 'maintenance' : 'online',
            players: Math.random() > 0.7 ? 0 : Math.floor(Math.random() * 300) + 600,
            maxPlayers: 2000
        }
    ];

    const serverCards = document.querySelectorAll('.server-card');

    servers.forEach((server, index) => {
        if (serverCards[index]) {
            const card = serverCards[index];
            const statusElement = card.querySelector('.server-status span:last-child');
            const populationFill = card.querySelector('.population-fill');
            const populationText = card.querySelector('.population-text');
            const statusDot = card.querySelector('.status-dot');

            // Update status
            card.className = `server-card ${server.status}`;
            statusElement.textContent = server.status === 'online' ? 'ออนไลน์' : 'ปรับปรุง';
            statusDot.className = `status-dot ${server.status}`;

            // Update population
            if (server.status === 'online') {
                const percentage = (server.players / server.maxPlayers) * 100;
                populationFill.style.width = `${percentage}%`;
                populationText.textContent = `${server.players} / ${server.maxPlayers}`;
                populationFill.className = 'population-fill';
            } else {
                populationFill.style.width = '0%';
                populationText.textContent = 'กำลังปรับปรุง';
                populationFill.className = 'population-fill maintenance';
            }
        }
    });

    // Update game stats
    updateGameStats();
}

function updateGameStats() {
    const totalPlayers = Math.floor(Math.random() * 10000) + 45000;
    const totalGuilds = Math.floor(Math.random() * 200) + 900;

    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 2) {
        statNumbers[0].textContent = `${totalPlayers.toLocaleString()}+`;
        statNumbers[1].textContent = `${totalGuilds.toLocaleString()}+`;
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Update server status every 30 seconds
    updateServerStatus();
    setInterval(updateServerStatus, 30000);

    // Load initial ranking data
    loadRankingData('level');

    // Server panel toggle
    const panelHeader = document.querySelector('.panel-header');
    if (panelHeader) {
        panelHeader.addEventListener('click', toggleServerPanel);
    }

    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Close trailer when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('trailer-modal')) {
            closeTrailer();
        }
    });
});
